import 'package:flutter/material.dart';
import '../services/strapi_service.dart';
import '../models/destination_model.dart';
import '../models/hotel_model.dart';
import '../models/restaurant_model.dart';
import '../models/activity_model.dart';
import '../models/category_model.dart';
import '../models/travel_tip_model.dart';
import '../config/app_config.dart';

class ComprehensiveDataScreen extends StatefulWidget {
  const ComprehensiveDataScreen({super.key});

  @override
  State<ComprehensiveDataScreen> createState() => _ComprehensiveDataScreenState();
}

class _ComprehensiveDataScreenState extends State<ComprehensiveDataScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  List<DestinationModel> destinations = [];
  List<HotelModel> hotels = [];
  List<RestaurantModel> restaurants = [];
  List<ActivityModel> activities = [];
  List<CategoryModel> categories = [];
  List<TravelTipModel> travelTips = [];

  bool isLoading = true;
  bool isConnected = false;
  String errorMessage = '';

  Map<String, bool> loadingStates = {
    'destinations': true,
    'hotels': true,
    'restaurants': true,
    'activities': true,
    'categories': true,
    'travelTips': true,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadAllData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAllData() async {
    setState(() {
      isLoading = true;
      errorMessage = '';
      loadingStates = {
        'destinations': true,
        'hotels': true,
        'restaurants': true,
        'activities': true,
        'categories': true,
        'travelTips': true,
      };
    });

    try {
      // Test connection first
      final connected = await StrapiService.testConnection();
      setState(() {
        isConnected = connected;
      });

      if (!connected) {
        setState(() {
          errorMessage = 'Cannot connect to Strapi CMS. Please check your connection.';
          isLoading = false;
        });
        return;
      }

      // Load all data in parallel
      final futures = [
        _loadDestinations(),
        _loadHotels(),
        _loadRestaurants(),
        _loadActivities(),
        _loadCategories(),
        _loadTravelTips(),
      ];

      await Future.wait(futures);

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Error loading data: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _loadDestinations() async {
    try {
      final fetchedDestinations = await StrapiService.getDestinations();
      setState(() {
        destinations = fetchedDestinations;
        loadingStates['destinations'] = false;
      });
    } catch (e) {
      setState(() {
        loadingStates['destinations'] = false;
      });
      print('Error loading destinations: $e');
    }
  }

  Future<void> _loadHotels() async {
    try {
      final fetchedHotels = await StrapiService.getHotels();
      setState(() {
        hotels = fetchedHotels;
        loadingStates['hotels'] = false;
      });
    } catch (e) {
      setState(() {
        loadingStates['hotels'] = false;
      });
      print('Error loading hotels: $e');
    }
  }

  Future<void> _loadRestaurants() async {
    try {
      final fetchedRestaurants = await StrapiService.getRestaurants();
      setState(() {
        restaurants = fetchedRestaurants;
        loadingStates['restaurants'] = false;
      });
    } catch (e) {
      setState(() {
        loadingStates['restaurants'] = false;
      });
      print('Error loading restaurants: $e');
    }
  }

  Future<void> _loadActivities() async {
    try {
      final fetchedActivities = await StrapiService.getActivities();
      setState(() {
        activities = fetchedActivities;
        loadingStates['activities'] = false;
      });
    } catch (e) {
      setState(() {
        loadingStates['activities'] = false;
      });
      print('Error loading activities: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final fetchedCategories = await StrapiService.getCategories();
      setState(() {
        categories = fetchedCategories;
        loadingStates['categories'] = false;
      });
    } catch (e) {
      setState(() {
        loadingStates['categories'] = false;
      });
      print('Error loading categories: $e');
    }
  }

  Future<void> _loadTravelTips() async {
    try {
      final fetchedTravelTips = await StrapiService.getTravelTips();
      setState(() {
        travelTips = fetchedTravelTips;
        loadingStates['travelTips'] = false;
      });
    } catch (e) {
      setState(() {
        loadingStates['travelTips'] = false;
      });
      print('Error loading travel tips: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${AppConfig.appShortName} - All Data'),
        backgroundColor: Color(AppConfig.primaryBlue),
        foregroundColor: Color(AppConfig.primaryForeground),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Color(AppConfig.primaryForeground),
          unselectedLabelColor: Color(AppConfig.primaryForeground).withValues(alpha: 0.7),
          indicatorColor: Color(AppConfig.primaryForeground),
          tabs: [
            Tab(text: 'Destinations (${destinations.length})'),
            Tab(text: 'Hotels (${hotels.length})'),
            Tab(text: 'Restaurants (${restaurants.length})'),
            Tab(text: 'Activities (${activities.length})'),
            Tab(text: 'Categories (${categories.length})'),
            Tab(text: 'Tips (${travelTips.length})'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Connection Status
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isConnected 
                  ? Colors.green.shade50 
                  : Colors.red.shade50,
              border: Border(
                bottom: BorderSide(
                  color: isConnected ? Colors.green : Colors.red,
                  width: 2,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isConnected ? Icons.check_circle : Icons.error,
                  color: isConnected ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isConnected 
                        ? 'Connected to Strapi CMS - Loading all data...' 
                        : 'Disconnected from Strapi CMS',
                    style: TextStyle(
                      color: isConnected ? Colors.green.shade800 : Colors.red.shade800,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: errorMessage.isNotEmpty
                ? _buildErrorView()
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildDestinationsTab(),
                      _buildHotelsTab(),
                      _buildRestaurantsTab(),
                      _buildActivitiesTab(),
                      _buildCategoriesTab(),
                      _buildTravelTipsTab(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Color(AppConfig.destructive),
            ),
            const SizedBox(height: 16),
            Text(
              'Connection Error',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(AppConfig.destructive),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: TextStyle(
                fontSize: 16,
                color: Color(AppConfig.mutedForeground),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAllData,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry Connection'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(AppConfig.primaryBlue),
                foregroundColor: Color(AppConfig.primaryForeground),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDestinationsTab() {
    if (loadingStates['destinations'] == true) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading destinations...'),
          ],
        ),
      );
    }

    if (destinations.isEmpty) {
      return _buildEmptyState(
        icon: Icons.location_off,
        title: 'No Destinations Found',
        subtitle: 'Add some destinations in the Strapi admin panel',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: destinations.length,
      itemBuilder: (context, index) {
        final destination = destinations[index];
        return _buildDestinationCard(destination);
      },
    );
  }

  Widget _buildHotelsTab() {
    if (loadingStates['hotels'] == true) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading hotels...'),
          ],
        ),
      );
    }

    if (hotels.isEmpty) {
      return _buildEmptyState(
        icon: Icons.hotel_outlined,
        title: 'No Hotels Found',
        subtitle: 'Add some hotels in the Strapi admin panel',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: hotels.length,
      itemBuilder: (context, index) {
        final hotel = hotels[index];
        return _buildHotelCard(hotel);
      },
    );
  }

  Widget _buildRestaurantsTab() {
    if (loadingStates['restaurants'] == true) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading restaurants...'),
          ],
        ),
      );
    }

    if (restaurants.isEmpty) {
      return _buildEmptyState(
        icon: Icons.restaurant_outlined,
        title: 'No Restaurants Found',
        subtitle: 'Add some restaurants in the Strapi admin panel',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: restaurants.length,
      itemBuilder: (context, index) {
        final restaurant = restaurants[index];
        return _buildRestaurantCard(restaurant);
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Color(AppConfig.mutedForeground),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Color(AppConfig.foreground),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                color: Color(AppConfig.mutedForeground),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDestinationCard(DestinationModel destination) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Color(AppConfig.primaryBlue),
          child: Text(
            destination.name.substring(0, 1).toUpperCase(),
            style: TextStyle(
              color: Color(AppConfig.primaryForeground),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          destination.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(destination.shortDescription ?? destination.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  destination.region,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.category,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  destination.category,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (destination.isFeatured)
              const Icon(
                Icons.star,
                color: Colors.amber,
                size: 20,
              ),
            if (destination.isPopular)
              const Icon(
                Icons.trending_up,
                color: Colors.green,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotelCard(HotelModel hotel) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Color(AppConfig.primaryBlue),
          child: Icon(
            Icons.hotel,
            color: Color(AppConfig.primaryForeground),
          ),
        ),
        title: Text(
          hotel.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (hotel.description.isNotEmpty)
              Text(
                hotel.description.length > 100
                    ? '${hotel.description.substring(0, 100)}...'
                    : hotel.description,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.location_city,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  hotel.city,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.star,
                  size: 16,
                  color: Colors.amber,
                ),
                const SizedBox(width: 4),
                Text(
                  '${hotel.rating}/5',
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: hotel.featured
            ? const Icon(
                Icons.verified,
                color: Colors.blue,
                size: 20,
              )
            : null,
      ),
    );
  }

  Widget _buildRestaurantCard(RestaurantModel restaurant) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Color(AppConfig.primaryBlue),
          child: Icon(
            Icons.restaurant,
            color: Color(AppConfig.primaryForeground),
          ),
        ),
        title: Text(
          restaurant.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (restaurant.description.isNotEmpty)
              Text(
                restaurant.description.length > 100
                    ? '${restaurant.description.substring(0, 100)}...'
                    : restaurant.description,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.location_city,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  restaurant.city,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.restaurant_menu,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  restaurant.cuisine,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: restaurant.featured
            ? const Icon(
                Icons.verified,
                color: Colors.blue,
                size: 20,
              )
            : null,
      ),
    );
  }

  Widget _buildActivitiesTab() {
    if (loadingStates['activities'] == true) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading activities...'),
          ],
        ),
      );
    }

    if (activities.isEmpty) {
      return _buildEmptyState(
        icon: Icons.local_activity_outlined,
        title: 'No Activities Found',
        subtitle: 'Add some activities in the Strapi admin panel',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return _buildActivityCard(activity);
      },
    );
  }

  Widget _buildCategoriesTab() {
    if (loadingStates['categories'] == true) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading categories...'),
          ],
        ),
      );
    }

    if (categories.isEmpty) {
      return _buildEmptyState(
        icon: Icons.category_outlined,
        title: 'No Categories Found',
        subtitle: 'Add some categories in the Strapi admin panel',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildTravelTipsTab() {
    if (loadingStates['travelTips'] == true) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading travel tips...'),
          ],
        ),
      );
    }

    if (travelTips.isEmpty) {
      return _buildEmptyState(
        icon: Icons.tips_and_updates_outlined,
        title: 'No Travel Tips Found',
        subtitle: 'Add some travel tips in the Strapi admin panel',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: travelTips.length,
      itemBuilder: (context, index) {
        final tip = travelTips[index];
        return _buildTravelTipCard(tip);
      },
    );
  }

  Widget _buildActivityCard(ActivityModel activity) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Color(AppConfig.primaryBlue),
          child: Icon(
            Icons.local_activity,
            color: Color(AppConfig.primaryForeground),
          ),
        ),
        title: Text(
          activity.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (activity.shortDescription?.isNotEmpty == true)
              Text(
                activity.shortDescription!.length > 100
                    ? '${activity.shortDescription!.substring(0, 100)}...'
                    : activity.shortDescription!,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  activity.duration,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.fitness_center,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  activity.difficulty,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (activity.isFeatured)
              const Icon(
                Icons.star,
                color: Colors.amber,
                size: 20,
              ),
            if (activity.isRecommended)
              const Icon(
                Icons.thumb_up,
                color: Colors.green,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(CategoryModel category) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: category.color != null
              ? Color(int.parse(category.color!.replaceFirst('#', '0xFF')))
              : Color(AppConfig.primaryBlue),
          child: Icon(
            Icons.category,
            color: Color(AppConfig.primaryForeground),
          ),
        ),
        title: Text(
          category.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (category.description?.isNotEmpty == true)
              Text(category.description!),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.label,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  category.type,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.sort,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  'Order: ${category.sortOrder}',
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!category.isActive)
              const Icon(
                Icons.visibility_off,
                color: Colors.grey,
                size: 20,
              ),
            if (category.hasSubCategories)
              const Icon(
                Icons.account_tree,
                color: Colors.blue,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTravelTipCard(TravelTipModel tip) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: tip.isCritical
              ? Colors.red
              : tip.isHighPriority
                  ? Colors.orange
                  : Color(AppConfig.primaryBlue),
          child: Icon(
            Icons.tips_and_updates,
            color: Color(AppConfig.primaryForeground),
          ),
        ),
        title: Text(
          tip.title,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (tip.excerpt?.isNotEmpty == true)
              Text(
                tip.excerpt!.length > 100
                    ? '${tip.excerpt!.substring(0, 100)}...'
                    : tip.excerpt!,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.category,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  tip.category,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: Color(AppConfig.mutedForeground),
                ),
                const SizedBox(width: 4),
                Text(
                  tip.formattedReadingTime,
                  style: TextStyle(
                    color: Color(AppConfig.mutedForeground),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (tip.isFeatured)
              const Icon(
                Icons.star,
                color: Colors.amber,
                size: 20,
              ),
            if (tip.isGeneral)
              const Icon(
                Icons.public,
                color: Colors.green,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
