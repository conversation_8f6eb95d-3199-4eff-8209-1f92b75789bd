class CategoryModel {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? icon;
  final String? color;
  final String type;
  final bool isActive;
  final int sortOrder;
  final int? parentCategoryId;
  final List<CategoryModel> subCategories;
  final DateTime createdAt;
  final DateTime updatedAt;

  CategoryModel({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.icon,
    this.color,
    required this.type,
    this.isActive = true,
    this.sortOrder = 0,
    this.parentCategoryId,
    this.subCategories = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? {};
    
    return CategoryModel(
      id: json['id'] ?? 0,
      name: attributes['name'] ?? '',
      slug: attributes['slug'] ?? '',
      description: attributes['description'],
      icon: _extractImageUrl(attributes['icon']),
      color: attributes['color'],
      type: attributes['type'] ?? 'General',
      isActive: attributes['isActive'] ?? true,
      sortOrder: attributes['sortOrder'] ?? 0,
      parentCategoryId: attributes['parentCategory']?['data']?['id'],
      subCategories: _parseSubCategories(attributes['subCategories']),
      createdAt: DateTime.parse(attributes['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(attributes['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'attributes': {
        'name': name,
        'slug': slug,
        'description': description,
        'color': color,
        'type': type,
        'isActive': isActive,
        'sortOrder': sortOrder,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      },
    };
  }

  static String? _extractImageUrl(dynamic imageData) {
    if (imageData == null) return null;
    if (imageData['data'] == null) return null;
    return imageData['data']['attributes']?['url'];
  }

  static List<CategoryModel> _parseSubCategories(dynamic subCategoriesData) {
    if (subCategoriesData == null || subCategoriesData['data'] == null) return [];
    final List<dynamic> data = subCategoriesData['data'];
    return data.map((item) => CategoryModel.fromJson(item)).toList();
  }

  // Helper methods
  bool get hasSubCategories => subCategories.isNotEmpty;
  
  bool get isParentCategory => parentCategoryId == null;
  
  bool get isSubCategory => parentCategoryId != null;

  // Get all categories in a flat list (including subcategories)
  List<CategoryModel> get flattenedCategories {
    List<CategoryModel> result = [this];
    for (var subCategory in subCategories) {
      result.addAll(subCategory.flattenedCategories);
    }
    return result;
  }

  // Find a subcategory by ID
  CategoryModel? findSubCategoryById(int id) {
    for (var subCategory in subCategories) {
      if (subCategory.id == id) return subCategory;
      var found = subCategory.findSubCategoryById(id);
      if (found != null) return found;
    }
    return null;
  }

  // Get categories by type
  static List<CategoryModel> filterByType(List<CategoryModel> categories, String type) {
    return categories.where((category) => category.type == type).toList();
  }

  // Get active categories only
  static List<CategoryModel> getActiveCategories(List<CategoryModel> categories) {
    return categories.where((category) => category.isActive).toList();
  }

  // Sort categories by sort order
  static List<CategoryModel> sortByOrder(List<CategoryModel> categories) {
    List<CategoryModel> sorted = List.from(categories);
    sorted.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
    return sorted;
  }

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name, type: $type, isActive: $isActive, subCategories: ${subCategories.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
