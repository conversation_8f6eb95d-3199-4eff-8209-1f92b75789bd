const axios = require('axios');

// Configuration
const STRAPI_URL = 'http://localhost:1337/api';
const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';

const headers = {
  'Authorization': `Bearer ${API_TOKEN}`,
  'Content-Type': 'application/json',
};

// Helper function to make API requests
async function createEntry(endpoint, data) {
  try {
    console.log(`Creating ${endpoint}...`);
    const response = await axios.post(`${STRAPI_URL}/${endpoint}`, { data }, { headers });
    console.log(`✅ Created ${endpoint}: ${response.data.data.attributes.name || response.data.data.attributes.title || response.data.data.id}`);
    return response.data.data;
  } catch (error) {
    console.error(`❌ Error creating ${endpoint}:`, error.response?.data || error.message);
    return null;
  }
}

// Helper function to check if entry exists
async function entryExists(endpoint, slug) {
  try {
    const response = await axios.get(`${STRAPI_URL}/${endpoint}?filters[slug][$eq]=${slug}`, { headers });
    return response.data.data.length > 0;
  } catch (error) {
    return false;
  }
}

// Migration data
const destinationsData = [
  {
    title: "Saranda",
    slug: "saranda",
    description: "Saranda is a beautiful coastal city in southern Albania, known for its stunning beaches along the Ionian Sea, crystal-clear waters, and proximity to ancient archaeological sites. This charming resort town offers a perfect blend of natural beauty, rich history, and vibrant nightlife, making it one of Albania's most popular tourist destinations.",
    excerpt: "Beautiful coastal city with stunning beaches and crystal-clear waters",
    region: "Southern Albania",
    country: "Albania",
    location: {
      latitude: 39.8759,
      longitude: 20.0106,
      address: "Saranda, Albania"
    },
    highlights: [
      {
        title: "Butrint National Park",
        description: "UNESCO World Heritage site with ancient ruins",
        icon: "🏛️"
      },
      {
        title: "Ksamil Beach",
        description: "Paradise-like beach with turquoise waters",
        icon: "🏖️"
      },
      {
        title: "Blue Eye Spring",
        description: "Natural spring with incredibly blue water",
        icon: "💙"
      }
    ],
    bestTimeToVisit: [
      {
        season: "Summer",
        months: "June - August",
        description: "Perfect for beach activities and swimming",
        temperature: "25-30°C"
      }
    ],
    rating: 4.8,
    reviewCount: 1250,
    featured: true,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Berat",
    slug: "berat",
    description: "Known as the 'City of a Thousand Windows', Berat is a UNESCO World Heritage site that showcases Albania's rich Ottoman heritage. This historic city features well-preserved medieval architecture, ancient castles, and traditional stone houses that cascade down hillsides, creating one of the most picturesque urban landscapes in the Balkans.",
    excerpt: "UNESCO World Heritage city known as the 'City of a Thousand Windows'",
    region: "Central Albania",
    country: "Albania",
    location: {
      latitude: 40.7058,
      longitude: 19.9522,
      address: "Berat, Albania"
    },
    highlights: [
      {
        title: "Berat Castle",
        description: "Medieval fortress with panoramic city views",
        icon: "🏰"
      },
      {
        title: "Mangalem Quarter",
        description: "Historic neighborhood with Ottoman architecture",
        icon: "🏘️"
      },
      {
        title: "Onufri Museum",
        description: "Byzantine art and religious artifacts",
        icon: "🎨"
      }
    ],
    bestTimeToVisit: [
      {
        season: "Spring/Fall",
        months: "April-May, September-October",
        description: "Ideal weather for exploring historic sites",
        temperature: "18-25°C"
      }
    ],
    rating: 4.6,
    reviewCount: 890,
    featured: true,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Theth National Park",
    slug: "theth-national-park",
    description: "Theth National Park is a pristine wilderness area in the Albanian Alps, offering some of the most spectacular mountain scenery in the Balkans. This remote valley features traditional stone houses, crystal-clear mountain streams, dramatic peaks, and hiking trails that lead to breathtaking waterfalls and viewpoints.",
    excerpt: "Pristine wilderness in the Albanian Alps with spectacular mountain scenery",
    region: "Northern Albania",
    country: "Albania",
    location: {
      latitude: 42.3889,
      longitude: 19.7694,
      address: "Theth, Shkodër, Albania"
    },
    highlights: [
      {
        title: "Blue Eye of Theth",
        description: "Natural pool with incredibly clear blue water",
        icon: "💎"
      },
      {
        title: "Grunas Waterfall",
        description: "Spectacular 30-meter high waterfall",
        icon: "💧"
      },
      {
        title: "Lock-in Tower",
        description: "Historic tower showcasing traditional Albanian culture",
        icon: "🗼"
      }
    ],
    bestTimeToVisit: [
      {
        season: "Summer",
        months: "June - September",
        description: "Best weather for hiking and outdoor activities",
        temperature: "15-25°C"
      }
    ],
    rating: 4.7,
    reviewCount: 456,
    featured: true,
    publishedAt: new Date().toISOString()
  }
];

const hotelsData = [
  {
    name: "Hotel Butrint",
    slug: "hotel-butrint-saranda",
    description: "Luxury beachfront hotel in Saranda with stunning views of the Ionian Sea. Perfect for exploring the Albanian Riviera with modern amenities and traditional Albanian hospitality.",
    excerpt: "Luxury beachfront hotel with stunning sea views in Saranda",
    city: "Saranda",
    region: "Southern Albania",
    country: "Albania",
    starRating: 4,
    priceRange: "Luxury",
    location: {
      latitude: 39.8759,
      longitude: 20.0106,
      address: "Rruga Jonianet, Saranda, Albania",
      city: "Saranda"
    },
    pricePerNight: {
      basePrice: 120,
      currency: "EUR",
      priceType: "per_night"
    },
    amenities: [
      { name: "Free WiFi", category: "Technology", available: true, additionalCost: false },
      { name: "Swimming Pool", category: "Recreation", available: true, additionalCost: false },
      { name: "Restaurant", category: "Food & Drink", available: true, additionalCost: false },
      { name: "Bar", category: "Food & Drink", available: true, additionalCost: false },
      { name: "Spa", category: "Recreation", available: true, additionalCost: true },
      { name: "Fitness Center", category: "Recreation", available: true, additionalCost: false },
      { name: "Beach Access", category: "Recreation", available: true, additionalCost: false },
      { name: "Parking", category: "Transportation", available: true, additionalCost: false }
    ],
    roomTypes: [
      {
        name: "Standard Double Room",
        description: "Comfortable room with sea view",
        maxOccupancy: 2,
        bedType: "Double Bed",
        size: 25,
        sizeUnit: "sqm",
        pricePerNight: 120,
        currency: "EUR",
        available: true
      },
      {
        name: "Junior Suite",
        description: "Spacious suite with balcony and sea view",
        maxOccupancy: 3,
        bedType: "King Bed",
        size: 40,
        sizeUnit: "sqm",
        pricePerNight: 180,
        currency: "EUR",
        available: true
      }
    ],
    checkInTime: "15:00",
    checkOutTime: "11:00",
    contactInfo: {
      phone: "+355 85 123 456",
      email: "<EMAIL>",
      website: "www.hotelbutrint.al"
    },
    policies: [
      {
        title: "Cancellation Policy",
        description: "Free cancellation up to 24 hours before check-in",
        type: "Cancellation",
        important: true
      },
      {
        title: "Pet Policy",
        description: "Pets are welcome with additional fee",
        type: "Pet",
        important: false
      }
    ],
    rating: 4.5,
    reviewCount: 324,
    featured: true,
    bookingUrl: "https://booking.com/hotel-butrint",
    website: "www.hotelbutrint.al",
    publishedAt: new Date().toISOString()
  },
  {
    name: "Tradita Geg & Tosk",
    slug: "tradita-geg-tosk-berat",
    description: "Traditional Albanian guesthouse in the heart of Berat's UNESCO World Heritage old town. Experience authentic Albanian hospitality in a beautifully restored Ottoman house.",
    excerpt: "Traditional guesthouse in Berat's historic old town",
    city: "Berat",
    region: "Central Albania",
    country: "Albania",
    starRating: 3,
    priceRange: "Mid-range",
    location: {
      latitude: 40.7058,
      longitude: 19.9522,
      address: "Rruga Mihal Komnena, Berat, Albania",
      city: "Berat"
    },
    pricePerNight: {
      basePrice: 45,
      currency: "EUR",
      priceType: "per_night"
    },
    amenities: [
      { name: "Free WiFi", category: "Technology", available: true, additionalCost: false },
      { name: "Traditional Restaurant", category: "Food & Drink", available: true, additionalCost: false },
      { name: "Terrace", category: "General", available: true, additionalCost: false },
      { name: "Historic Building", category: "General", available: true, additionalCost: false },
      { name: "City Center Location", category: "General", available: true, additionalCost: false }
    ],
    roomTypes: [
      {
        name: "Traditional Double Room",
        description: "Authentic room with traditional Albanian decor",
        maxOccupancy: 2,
        bedType: "Double Bed",
        size: 20,
        sizeUnit: "sqm",
        pricePerNight: 45,
        currency: "EUR",
        available: true
      }
    ],
    checkInTime: "14:00",
    checkOutTime: "11:00",
    contactInfo: {
      phone: "+355 32 232 122",
      email: "<EMAIL>"
    },
    policies: [
      {
        title: "Check-in Policy",
        description: "Check-in after 14:00, late check-in available",
        type: "Check-in",
        important: false
      }
    ],
    rating: 4.2,
    reviewCount: 156,
    featured: false,
    publishedAt: new Date().toISOString()
  }
];

const restaurantsData = [
  {
    name: "Limani Restaurant",
    slug: "limani-restaurant-saranda",
    description: "Premium seafood restaurant on Saranda's waterfront serving fresh catch of the day and traditional Albanian coastal cuisine. Stunning sunset views over the Ionian Sea.",
    excerpt: "Premium seafood restaurant with waterfront views in Saranda",
    city: "Saranda",
    region: "Southern Albania",
    country: "Albania",
    cuisine: "Seafood",
    priceRange: "Fine Dining",
    location: {
      latitude: 39.8759,
      longitude: 20.0106,
      address: "Rruga Jonianet, Saranda, Albania",
      city: "Saranda"
    },
    averagePrice: {
      basePrice: 35,
      currency: "EUR",
      priceType: "per_person"
    },
    openingHours: [
      { day: "Monday", openTime: "12:00", closeTime: "23:00", closed: false },
      { day: "Tuesday", openTime: "12:00", closeTime: "23:00", closed: false },
      { day: "Wednesday", openTime: "12:00", closeTime: "23:00", closed: false },
      { day: "Thursday", openTime: "12:00", closeTime: "23:00", closed: false },
      { day: "Friday", openTime: "12:00", closeTime: "24:00", closed: false },
      { day: "Saturday", openTime: "12:00", closeTime: "24:00", closed: false },
      { day: "Sunday", openTime: "12:00", closeTime: "23:00", closed: false }
    ],
    specialties: [
      {
        name: "Grilled Sea Bass",
        description: "Fresh sea bass grilled with Mediterranean herbs and olive oil",
        price: 18,
        currency: "EUR",
        ingredients: ["Sea Bass", "Olive Oil", "Mediterranean Herbs", "Lemon"],
        allergens: ["Fish"],
        dietary: "None",
        spicyLevel: 0,
        popular: true
      },
      {
        name: "Seafood Risotto",
        description: "Creamy risotto with mixed seafood and saffron",
        price: 16,
        currency: "EUR",
        ingredients: ["Arborio Rice", "Mixed Seafood", "Saffron", "White Wine"],
        allergens: ["Shellfish", "Dairy"],
        dietary: "None",
        spicyLevel: 0,
        popular: true
      },
      {
        name: "Albanian Fish Soup",
        description: "Traditional fish soup with local vegetables",
        price: 12,
        currency: "EUR",
        ingredients: ["Fresh Fish", "Tomatoes", "Onions", "Local Herbs"],
        allergens: ["Fish"],
        dietary: "None",
        spicyLevel: 1,
        popular: false
      }
    ],
    features: [
      { name: "Waterfront Terrace", description: "Beautiful terrace overlooking the sea", category: "Atmosphere", available: true },
      { name: "Fresh Seafood Daily", description: "Daily fresh catch from local fishermen", category: "Service", available: true },
      { name: "Wine Selection", description: "Curated selection of Albanian and international wines", category: "Service", available: true },
      { name: "Sunset Views", description: "Perfect spot to watch the sunset", category: "Atmosphere", available: true }
    ],
    contactInfo: {
      phone: "+355 85 234 567",
      email: "<EMAIL>",
      website: "www.limanirestaurant.al"
    },
    rating: 4.6,
    reviewCount: 287,
    featured: true,
    reservationRequired: true,
    reservationUrl: "https://limanirestaurant.al/reservations",
    website: "www.limanirestaurant.al",
    publishedAt: new Date().toISOString()
  },
  {
    name: "Mangalemi Restaurant",
    slug: "mangalemi-restaurant-berat",
    description: "Traditional Albanian restaurant in Berat's historic Mangalem quarter. Serving authentic regional dishes in a beautifully restored Ottoman house with panoramic city views.",
    excerpt: "Traditional Albanian cuisine in historic Berat setting",
    city: "Berat",
    region: "Central Albania",
    country: "Albania",
    cuisine: "Albanian Traditional",
    priceRange: "Mid-range",
    location: {
      latitude: 40.7058,
      longitude: 19.9522,
      address: "Lagja Mangalem, Berat, Albania",
      city: "Berat"
    },
    averagePrice: {
      basePrice: 18,
      currency: "EUR",
      priceType: "per_person"
    },
    openingHours: [
      { day: "Monday", openTime: "11:00", closeTime: "22:00", closed: false },
      { day: "Tuesday", openTime: "11:00", closeTime: "22:00", closed: false },
      { day: "Wednesday", openTime: "11:00", closeTime: "22:00", closed: false },
      { day: "Thursday", openTime: "11:00", closeTime: "22:00", closed: false },
      { day: "Friday", openTime: "11:00", closeTime: "23:00", closed: false },
      { day: "Saturday", openTime: "11:00", closeTime: "23:00", closed: false },
      { day: "Sunday", openTime: "11:00", closeTime: "22:00", closed: false }
    ],
    specialties: [
      {
        name: "Tavë Kosi",
        description: "Traditional baked lamb with yogurt and rice",
        price: 14,
        currency: "EUR",
        ingredients: ["Lamb", "Yogurt", "Rice", "Eggs", "Albanian Herbs"],
        allergens: ["Dairy", "Eggs"],
        dietary: "None",
        spicyLevel: 0,
        popular: true
      },
      {
        name: "Byrek me Spinaq",
        description: "Homemade spinach pie with feta cheese",
        price: 8,
        currency: "EUR",
        ingredients: ["Phyllo Pastry", "Spinach", "Feta Cheese", "Eggs", "Olive Oil"],
        allergens: ["Gluten", "Dairy", "Eggs"],
        dietary: "Vegetarian",
        spicyLevel: 0,
        popular: true
      },
      {
        name: "Qofte të Fërguara",
        description: "Traditional Albanian meatballs with herbs",
        price: 10,
        currency: "EUR",
        ingredients: ["Ground Beef", "Onions", "Albanian Herbs", "Breadcrumbs"],
        allergens: ["Gluten"],
        dietary: "None",
        spicyLevel: 1,
        popular: false
      }
    ],
    features: [
      { name: "Historic Setting", description: "Dining in a restored Ottoman house", category: "Atmosphere", available: true },
      { name: "Traditional Recipes", description: "Authentic family recipes passed down generations", category: "Service", available: true },
      { name: "Local Ingredients", description: "Fresh ingredients from local farmers", category: "Service", available: true },
      { name: "City Views", description: "Panoramic views of Berat's historic center", category: "Atmosphere", available: true }
    ],
    contactInfo: {
      phone: "+355 32 245 678",
      email: "<EMAIL>"
    },
    rating: 4.3,
    reviewCount: 198,
    featured: true,
    reservationRequired: false,
    publishedAt: new Date().toISOString()
  },
  {
    name: "Kulla e Ngujimit",
    slug: "kulla-ngujimit-theth",
    description: "Traditional mountain restaurant in Theth serving hearty Albanian mountain cuisine. Located in a historic stone tower with authentic atmosphere and local specialties.",
    excerpt: "Traditional mountain cuisine in historic stone tower",
    city: "Theth",
    region: "Northern Albania",
    country: "Albania",
    cuisine: "Albanian Traditional",
    priceRange: "Budget",
    location: {
      latitude: 42.3889,
      longitude: 19.7694,
      address: "Theth, Shkodër, Albania",
      city: "Theth"
    },
    averagePrice: {
      basePrice: 12,
      currency: "EUR",
      priceType: "per_person"
    },
    openingHours: [
      { day: "Monday", openTime: "08:00", closeTime: "21:00", closed: false },
      { day: "Tuesday", openTime: "08:00", closeTime: "21:00", closed: false },
      { day: "Wednesday", openTime: "08:00", closeTime: "21:00", closed: false },
      { day: "Thursday", openTime: "08:00", closeTime: "21:00", closed: false },
      { day: "Friday", openTime: "08:00", closeTime: "21:00", closed: false },
      { day: "Saturday", openTime: "08:00", closeTime: "21:00", closed: false },
      { day: "Sunday", openTime: "08:00", closeTime: "21:00", closed: false }
    ],
    specialties: [
      {
        name: "Flija",
        description: "Traditional layered pancake cooked over open fire",
        price: 8,
        currency: "EUR",
        ingredients: ["Flour", "Milk", "Eggs", "Butter"],
        allergens: ["Gluten", "Dairy", "Eggs"],
        dietary: "Vegetarian",
        spicyLevel: 0,
        popular: true
      },
      {
        name: "Mountain Lamb Stew",
        description: "Slow-cooked lamb with mountain herbs and vegetables",
        price: 15,
        currency: "EUR",
        ingredients: ["Lamb", "Mountain Herbs", "Potatoes", "Carrots", "Onions"],
        allergens: [],
        dietary: "None",
        spicyLevel: 1,
        popular: true
      },
      {
        name: "Corn Bread",
        description: "Fresh baked corn bread with local butter",
        price: 4,
        currency: "EUR",
        ingredients: ["Corn Flour", "Butter", "Eggs", "Milk"],
        allergens: ["Dairy", "Eggs"],
        dietary: "Vegetarian",
        spicyLevel: 0,
        popular: false
      }
    ],
    features: [
      { name: "Historic Stone Tower", description: "Dining in an authentic Albanian kulla", category: "Atmosphere", available: true },
      { name: "Open Fire Cooking", description: "Traditional cooking methods over open fire", category: "Service", available: true },
      { name: "Mountain Views", description: "Spectacular views of the Albanian Alps", category: "Atmosphere", available: true },
      { name: "Traditional Atmosphere", description: "Authentic mountain hospitality", category: "Atmosphere", available: true }
    ],
    contactInfo: {
      phone: "+355 69 345 678"
    },
    rating: 4.1,
    reviewCount: 76,
    featured: false,
    reservationRequired: false,
    publishedAt: new Date().toISOString()
  }
];

// Main migration function
async function runMigration() {
  console.log('🚀 Starting Strapi CMS data migration...\n');

  try {
    // Test connection
    console.log('Testing connection to Strapi...');
    await axios.get(`${STRAPI_URL}/destinations`, { headers });
    console.log('✅ Connected to Strapi successfully\n');

    // Migrate destinations
    console.log('📍 Migrating destinations...');
    for (const destination of destinationsData) {
      if (!(await entryExists('destinations', destination.slug))) {
        await createEntry('destinations', destination);
      } else {
        console.log(`⏭️  Destination ${destination.name} already exists, skipping...`);
      }
    }

    // Migrate hotels
    console.log('\n🏨 Migrating hotels...');
    for (const hotel of hotelsData) {
      if (!(await entryExists('hotels', hotel.slug))) {
        await createEntry('hotels', hotel);
      } else {
        console.log(`⏭️  Hotel ${hotel.name} already exists, skipping...`);
      }
    }

    // Migrate restaurants
    console.log('\n🍽️  Migrating restaurants...');
    for (const restaurant of restaurantsData) {
      if (!(await entryExists('restaurants', restaurant.slug))) {
        await createEntry('restaurants', restaurant);
      } else {
        console.log(`⏭️  Restaurant ${restaurant.name} already exists, skipping...`);
      }
    }

    console.log('\n✅ Migration completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Destinations: ${destinationsData.length} entries`);
    console.log(`- Hotels: ${hotelsData.length} entries`);
    console.log(`- Restaurants: ${restaurantsData.length} entries`);

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };
