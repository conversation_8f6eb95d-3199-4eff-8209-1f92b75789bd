const axios = require('axios');

async function testConnection() {
  try {
    console.log('🔍 Testing Strapi connection...');
    
    // Test if server is running
    const healthCheck = await axios.get('http://localhost:1337/_health', {
      timeout: 5000
    });
    
    console.log('✅ Strapi server is running');
    console.log('Health status:', healthCheck.status);
    
    // Test public API access (should be forbidden but shows server is responding)
    try {
      const publicTest = await axios.get('http://localhost:1337/api/destinations');
      console.log('✅ Public API accessible');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('⚠️ Public API is protected (expected)');
      } else {
        console.log('❌ Public API error:', error.response?.status);
      }
    }
    
    console.log('🌐 Admin panel should be accessible at: http://localhost:1337/admin');
    
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
  }
}

testConnection();
