class TravelTipModel {
  final int id;
  final String title;
  final String slug;
  final String content;
  final String? excerpt;
  final String? featuredImage;
  final String category;
  final List<int> destinationIds;
  final List<String> tags;
  final bool isGeneral;
  final bool isFeatured;
  final String priority;
  final String? author;
  final int? readingTime;
  final DateTime? lastUpdated;
  final DateTime createdAt;
  final DateTime updatedAt;

  TravelTipModel({
    required this.id,
    required this.title,
    required this.slug,
    required this.content,
    this.excerpt,
    this.featuredImage,
    required this.category,
    this.destinationIds = const [],
    this.tags = const [],
    this.isGeneral = true,
    this.isFeatured = false,
    this.priority = 'Medium',
    this.author,
    this.readingTime,
    this.lastUpdated,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TravelTipModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? {};
    
    return TravelTipModel(
      id: json['id'] ?? 0,
      title: attributes['title'] ?? '',
      slug: attributes['slug'] ?? '',
      content: attributes['content'] ?? '',
      excerpt: attributes['excerpt'],
      featuredImage: _extractImageUrl(attributes['featuredImage']),
      category: attributes['category'] ?? '',
      destinationIds: _parseDestinationIds(attributes['destinations']),
      tags: _parseStringList(attributes['tags']),
      isGeneral: attributes['isGeneral'] ?? true,
      isFeatured: attributes['isFeatured'] ?? false,
      priority: attributes['priority'] ?? 'Medium',
      author: attributes['author'],
      readingTime: attributes['readingTime'],
      lastUpdated: attributes['lastUpdated'] != null 
          ? DateTime.parse(attributes['lastUpdated'])
          : null,
      createdAt: DateTime.parse(attributes['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(attributes['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'attributes': {
        'title': title,
        'slug': slug,
        'content': content,
        'excerpt': excerpt,
        'category': category,
        'tags': tags,
        'isGeneral': isGeneral,
        'isFeatured': isFeatured,
        'priority': priority,
        'author': author,
        'readingTime': readingTime,
        'lastUpdated': lastUpdated?.toIso8601String(),
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      },
    };
  }

  static String? _extractImageUrl(dynamic imageData) {
    if (imageData == null) return null;
    if (imageData['data'] == null) return null;
    return imageData['data']['attributes']?['url'];
  }

  static List<int> _parseDestinationIds(dynamic destinationsData) {
    if (destinationsData == null || destinationsData['data'] == null) return [];
    final List<dynamic> data = destinationsData['data'];
    return data
        .map((item) => item['id'] as int?)
        .where((id) => id != null)
        .cast<int>()
        .toList();
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((item) => item.toString()).toList();
    }
    if (value is String) {
      // If it's a single string, return it as a list
      return [value];
    }
    return [];
  }

  // Helper methods
  bool get hasDestinations => destinationIds.isNotEmpty;
  
  bool get hasTags => tags.isNotEmpty;
  
  bool get hasReadingTime => readingTime != null && readingTime! > 0;
  
  bool get isHighPriority => priority == 'High' || priority == 'Critical';
  
  bool get isCritical => priority == 'Critical';

  // Get estimated reading time if not provided
  int get estimatedReadingTime {
    if (readingTime != null) return readingTime!;
    
    // Estimate based on content length (average 200 words per minute)
    final wordCount = content.split(' ').length;
    return (wordCount / 200).ceil().clamp(1, 60); // Min 1 minute, max 60 minutes
  }

  // Get formatted reading time
  String get formattedReadingTime {
    final time = estimatedReadingTime;
    if (time == 1) return '1 minute read';
    return '$time minutes read';
  }

  // Filter methods
  static List<TravelTipModel> filterByCategory(List<TravelTipModel> tips, String category) {
    return tips.where((tip) => tip.category == category).toList();
  }

  static List<TravelTipModel> getFeaturedTips(List<TravelTipModel> tips) {
    return tips.where((tip) => tip.isFeatured).toList();
  }

  static List<TravelTipModel> getGeneralTips(List<TravelTipModel> tips) {
    return tips.where((tip) => tip.isGeneral).toList();
  }

  static List<TravelTipModel> getHighPriorityTips(List<TravelTipModel> tips) {
    return tips.where((tip) => tip.isHighPriority).toList();
  }

  static List<TravelTipModel> filterByDestination(List<TravelTipModel> tips, int destinationId) {
    return tips.where((tip) => tip.destinationIds.contains(destinationId)).toList();
  }

  static List<TravelTipModel> searchByKeyword(List<TravelTipModel> tips, String keyword) {
    final lowerKeyword = keyword.toLowerCase();
    return tips.where((tip) => 
      tip.title.toLowerCase().contains(lowerKeyword) ||
      tip.content.toLowerCase().contains(lowerKeyword) ||
      (tip.excerpt?.toLowerCase().contains(lowerKeyword) ?? false) ||
      tip.tags.any((tag) => tag.toLowerCase().contains(lowerKeyword))
    ).toList();
  }

  // Sort methods
  static List<TravelTipModel> sortByPriority(List<TravelTipModel> tips) {
    final priorityOrder = {'Critical': 0, 'High': 1, 'Medium': 2, 'Low': 3};
    List<TravelTipModel> sorted = List.from(tips);
    sorted.sort((a, b) => 
      (priorityOrder[a.priority] ?? 2).compareTo(priorityOrder[b.priority] ?? 2)
    );
    return sorted;
  }

  static List<TravelTipModel> sortByDate(List<TravelTipModel> tips, {bool ascending = false}) {
    List<TravelTipModel> sorted = List.from(tips);
    sorted.sort((a, b) => ascending 
        ? a.updatedAt.compareTo(b.updatedAt)
        : b.updatedAt.compareTo(a.updatedAt)
    );
    return sorted;
  }

  @override
  String toString() {
    return 'TravelTipModel(id: $id, title: $title, category: $category, priority: $priority, isFeatured: $isFeatured)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TravelTipModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
