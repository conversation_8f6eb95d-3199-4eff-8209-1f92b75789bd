class ActivityModel {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String? shortDescription;
  final String? featuredImage;
  final List<String> gallery;
  final double? latitude;
  final double? longitude;
  final String? address;
  final int? destinationId;
  final List<String> category;
  final String difficulty;
  final String duration;
  final double? price;
  final String currency;
  final String? priceIncludes;
  final String? priceExcludes;
  final int minParticipants;
  final int? maxParticipants;
  final String? ageRestriction;
  final String? requirements;
  final String? whatToBring;
  final String? meetingPoint;
  final String? phone;
  final String? email;
  final String? bookingUrl;
  final bool isRecommended;
  final bool isFeatured;
  final bool isAvailableYearRound;
  final List<String> bestTimeToVisit;
  final double rating;
  final int reviewCount;
  final String? cancellationPolicy;
  final DateTime createdAt;
  final DateTime updatedAt;

  ActivityModel({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    this.shortDescription,
    this.featuredImage,
    this.gallery = const [],
    this.latitude,
    this.longitude,
    this.address,
    this.destinationId,
    this.category = const [],
    this.difficulty = 'Easy',
    required this.duration,
    this.price,
    this.currency = 'ALL',
    this.priceIncludes,
    this.priceExcludes,
    this.minParticipants = 1,
    this.maxParticipants,
    this.ageRestriction,
    this.requirements,
    this.whatToBring,
    this.meetingPoint,
    this.phone,
    this.email,
    this.bookingUrl,
    this.isRecommended = false,
    this.isFeatured = false,
    this.isAvailableYearRound = true,
    this.bestTimeToVisit = const [],
    this.rating = 0.0,
    this.reviewCount = 0,
    this.cancellationPolicy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? {};
    
    return ActivityModel(
      id: json['id'] ?? 0,
      name: attributes['name'] ?? '',
      slug: attributes['slug'] ?? '',
      description: attributes['description'] ?? '',
      shortDescription: attributes['shortDescription'],
      featuredImage: _extractImageUrl(attributes['featuredImage']),
      gallery: _extractGalleryUrls(attributes['gallery']),
      latitude: _parseDouble(attributes['latitude']),
      longitude: _parseDouble(attributes['longitude']),
      address: attributes['address'],
      destinationId: attributes['destination']?['data']?['id'],
      category: _parseStringList(attributes['category']),
      difficulty: attributes['difficulty'] ?? 'Easy',
      duration: attributes['duration'] ?? '',
      price: _parseDouble(attributes['price']),
      currency: attributes['currency'] ?? 'ALL',
      priceIncludes: attributes['priceIncludes'],
      priceExcludes: attributes['priceExcludes'],
      minParticipants: attributes['minParticipants'] ?? 1,
      maxParticipants: attributes['maxParticipants'],
      ageRestriction: attributes['ageRestriction'],
      requirements: attributes['requirements'],
      whatToBring: attributes['whatToBring'],
      meetingPoint: attributes['meetingPoint'],
      phone: attributes['phone'],
      email: attributes['email'],
      bookingUrl: attributes['bookingUrl'],
      isRecommended: attributes['isRecommended'] ?? false,
      isFeatured: attributes['isFeatured'] ?? false,
      isAvailableYearRound: attributes['isAvailableYearRound'] ?? true,
      bestTimeToVisit: _parseStringList(attributes['bestTimeToVisit']),
      rating: _parseDouble(attributes['rating']) ?? 0.0,
      reviewCount: attributes['reviewCount'] ?? 0,
      cancellationPolicy: attributes['cancellationPolicy'],
      createdAt: DateTime.parse(attributes['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(attributes['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'attributes': {
        'name': name,
        'slug': slug,
        'description': description,
        'shortDescription': shortDescription,
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
        'category': category,
        'difficulty': difficulty,
        'duration': duration,
        'price': price,
        'currency': currency,
        'priceIncludes': priceIncludes,
        'priceExcludes': priceExcludes,
        'minParticipants': minParticipants,
        'maxParticipants': maxParticipants,
        'ageRestriction': ageRestriction,
        'requirements': requirements,
        'whatToBring': whatToBring,
        'meetingPoint': meetingPoint,
        'phone': phone,
        'email': email,
        'bookingUrl': bookingUrl,
        'isRecommended': isRecommended,
        'isFeatured': isFeatured,
        'isAvailableYearRound': isAvailableYearRound,
        'bestTimeToVisit': bestTimeToVisit,
        'rating': rating,
        'reviewCount': reviewCount,
        'cancellationPolicy': cancellationPolicy,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      },
    };
  }

  static String? _extractImageUrl(dynamic imageData) {
    if (imageData == null) return null;
    if (imageData['data'] == null) return null;
    return imageData['data']['attributes']?['url'];
  }

  static List<String> _extractGalleryUrls(dynamic galleryData) {
    if (galleryData == null || galleryData['data'] == null) return [];
    final List<dynamic> data = galleryData['data'];
    return data
        .map((item) => item['attributes']?['url'] as String?)
        .where((url) => url != null)
        .cast<String>()
        .toList();
  }

  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((item) => item.toString()).toList();
    }
    return [];
  }

  @override
  String toString() {
    return 'ActivityModel(id: $id, name: $name, category: $category, difficulty: $difficulty, duration: $duration, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
