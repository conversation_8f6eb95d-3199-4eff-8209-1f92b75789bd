import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'firebase_options.dart';
import 'screens/main_navigation.dart';
import 'screens/auth/login_screen.dart';
import 'services/app_service.dart';
import 'models/user_model.dart';
import 'config/app_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase with options
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialized successfully!');

    // Create app service but don't wait for initialization
    final appService = AppService();
    // Add debug listener to user stream
    appService.userStream.listen((user) {
      print('🔊 Main - UserStream emitted: ${user?.name ?? "null"}');
    });
    // Initialize in background - don't await
    appService.initialize();

    runApp(AlbaniaVacationGuideApp(appService: appService));
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
    // Run app without Firebase for now
    runApp(const AlbaniaVacationGuideApp(appService: null));
  }
}

class AlbaniaVacationGuideApp extends StatelessWidget {
  final AppService? appService;

  const AlbaniaVacationGuideApp({super.key, required this.appService});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '${AppConfig.appName} - Discover Albania',
      debugShowCheckedModeBanner: false,
      theme: _buildLightTheme(),
      darkTheme: _buildDarkTheme(),
      themeMode: ThemeMode.system,
      home: appService != null
        ? AuthWrapper(appService: appService!)
        : LoginScreen(appService: AppService()), // Fallback without Firebase
    );
  }

  ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.light(
        primary: Color(AppConfig.primaryBlue),
        onPrimary: Color(AppConfig.primaryForeground),
        secondary: Color(AppConfig.secondary),
        onSecondary: Color(AppConfig.foreground),
        surface: Color(AppConfig.background),
        onSurface: Color(AppConfig.foreground),
        error: Color(AppConfig.destructive),
        onError: Color(AppConfig.destructiveForeground),
        outline: Color(AppConfig.border),
      ),
      textTheme: GoogleFonts.poppinsTextTheme(ThemeData.light().textTheme).copyWith(
        bodyLarge: GoogleFonts.poppins(color: Color(AppConfig.foreground)),
        bodyMedium: GoogleFonts.poppins(color: Color(AppConfig.foreground)),
        bodySmall: GoogleFonts.poppins(color: Color(AppConfig.mutedForeground)),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        titleTextStyle: GoogleFonts.poppins(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Color(AppConfig.foreground),
        ),
        iconTheme: IconThemeData(color: Color(AppConfig.foreground)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(AppConfig.primaryBlue),
          foregroundColor: Color(AppConfig.primaryForeground),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Color(AppConfig.border)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Color(AppConfig.border)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Color(AppConfig.ring), width: 2),
        ),
        fillColor: Color(AppConfig.input),
        filled: true,
      ),
      cardTheme: CardTheme(
        color: Color(AppConfig.background),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Color(AppConfig.border), width: 1),
        ),
      ),
    );
  }

  ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.dark(
        primary: Color(AppConfig.primaryBlueDark),
        onPrimary: Color(AppConfig.primaryForegroundDark),
        secondary: Color(AppConfig.secondaryDark),
        onSecondary: Color(AppConfig.foregroundDark),
        surface: Color(AppConfig.backgroundDark),
        onSurface: Color(AppConfig.foregroundDark),
        error: Color(AppConfig.destructiveDark),
        onError: Color(AppConfig.destructiveForeground),
        outline: Color(AppConfig.borderDark),
      ),
      textTheme: GoogleFonts.poppinsTextTheme(ThemeData.dark().textTheme).copyWith(
        bodyLarge: GoogleFonts.poppins(color: Color(AppConfig.foregroundDark)),
        bodyMedium: GoogleFonts.poppins(color: Color(AppConfig.foregroundDark)),
        bodySmall: GoogleFonts.poppins(color: Color(AppConfig.mutedForegroundDark)),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        titleTextStyle: GoogleFonts.poppins(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Color(AppConfig.foregroundDark),
        ),
        iconTheme: IconThemeData(color: Color(AppConfig.foregroundDark)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(AppConfig.primaryBlueDark),
          foregroundColor: Color(AppConfig.primaryForegroundDark),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Color(AppConfig.borderDark)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Color(AppConfig.borderDark)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Color(AppConfig.ringDark), width: 2),
        ),
        fillColor: Color(AppConfig.inputDark),
        filled: true,
      ),
      cardTheme: CardTheme(
        color: Color(AppConfig.backgroundDark),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Color(AppConfig.borderDark), width: 1),
        ),
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  final AppService appService;

  const AuthWrapper({super.key, required this.appService});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UserModel?>(
      stream: widget.appService.userStream,
      initialData: widget.appService.currentUser, // Use current user as initial data
      builder: (context, snapshot) {
        // Check if user is authenticated (from stream or current user)
        final user = snapshot.data ?? widget.appService.currentUser;
        print('🔍 AuthWrapper StreamBuilder - hasData: ${snapshot.hasData}, user: ${user?.name ?? "null"}');
        if (user != null) {
          print('✅ User authenticated: ${user.name} (${user.email})');
          return MainNavigation(appService: widget.appService);
        } else {
          print('🔐 No user authenticated, showing login screen');
          return LoginScreen(appService: widget.appService);
        }
      },
    );
  }
}


