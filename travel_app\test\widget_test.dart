// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:albania_vacation_guide/main.dart';

void main() {
  testWidgets('Albania Vacation Guide app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const AlbaniaVacationGuideApp(appService: null));

    // Verify that the app loads with the home screen
    expect(find.text('Discover Albania!'), findsOneWidget);
    expect(find.text('Where do you want to explore?'), findsOneWidget);
  });
}
