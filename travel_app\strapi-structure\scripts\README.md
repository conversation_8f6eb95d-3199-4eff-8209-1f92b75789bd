# Strapi CMS Migration Scripts

This directory contains migration scripts to populate your Strapi CMS with travel app data including destinations, hotels, and restaurants.

## Prerequisites

1. **Strapi CMS running**: Make sure your Strapi CMS is running on `http://localhost:1337`
2. **API Token**: The migration script uses the provided API token for authentication
3. **Content Types**: Ensure all content types are properly configured in Strapi:
   - Destinations
   - Hotels
   - Restaurants
   - Categories
   - Tags

## Installation

1. Navigate to the scripts directory:
```bash
cd travel_app/strapi-structure/scripts
```

2. Install dependencies:
```bash
npm install
```

## Running the Migration

### Option 1: Using npm script
```bash
npm run migrate
```

### Option 2: Direct node execution
```bash
node migrate-data.js
```

## What the Migration Does

The migration script will populate your Strapi CMS with:

### 🏛️ Destinations (3 entries)
- **Saranda**: Beautiful coastal city with stunning beaches
- **Berat**: UNESCO World Heritage "City of a Thousand Windows"
- **Theth National Park**: Pristine wilderness in Albanian Alps

### 🏨 Hotels (2 entries)
- **Hotel Butrint** (Saranda): Luxury beachfront hotel
- **Tradita Geg & Tosk** (Berat): Traditional guesthouse in historic old town

### 🍽️ Restaurants (3 entries)
- **Limani Restaurant** (Saranda): Premium seafood with waterfront views
- **Mangalemi Restaurant** (Berat): Traditional Albanian cuisine in historic setting
- **Kulla e Ngujimit** (Theth): Mountain cuisine in historic stone tower

## Features

- ✅ **Duplicate Prevention**: Checks if entries already exist before creating
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Rich Data**: Includes detailed information like amenities, specialties, opening hours
- ✅ **Authentic Content**: Real Albanian destinations with accurate information
- ✅ **Structured Data**: Properly formatted for Strapi's component system

## Configuration

The migration script is configured with:
- **Strapi URL**: `http://localhost:1337/api`
- **API Token**: Your provided authentication token
- **Content Types**: destinations, hotels, restaurants

## Troubleshooting

### Connection Issues
- Ensure Strapi is running on port 1337
- Check that the API token is valid
- Verify network connectivity

### Content Type Errors
- Make sure all content types are published in Strapi
- Verify component structures match the data format
- Check that required fields are properly configured

### Permission Issues
- Ensure the API token has create permissions for all content types
- Check that the token hasn't expired

## Data Structure

Each content type includes:

### Destinations
- Basic info (name, description, location)
- Highlights and attractions
- Best time to visit information
- Geographic coordinates

### Hotels
- Star ratings and price ranges
- Amenities and room types
- Contact information and policies
- Location and booking details

### Restaurants
- Cuisine types and price ranges
- Opening hours and specialties
- Menu items and features
- Contact and reservation info

## Success Output

When successful, you'll see output like:
```
🚀 Starting Strapi CMS data migration...

Testing connection to Strapi...
✅ Connected to Strapi successfully

📍 Migrating destinations...
✅ Created destinations: Saranda
✅ Created destinations: Berat
✅ Created destinations: Theth National Park

🏨 Migrating hotels...
✅ Created hotels: Hotel Butrint
✅ Created hotels: Tradita Geg & Tosk

🍽️ Migrating restaurants...
✅ Created restaurants: Limani Restaurant
✅ Created restaurants: Mangalemi Restaurant
✅ Created restaurants: Kulla e Ngujimit

✅ Migration completed successfully!

📊 Summary:
- Destinations: 3 entries
- Hotels: 2 entries
- Restaurants: 3 entries
```

## Next Steps

After running the migration:
1. Check your Strapi admin panel to verify the data
2. Test the Flutter app to ensure data is being fetched correctly
3. Add more content as needed through the Strapi admin interface
4. Configure any additional relationships between content types
