class AppConfig {
  // App Information
  static const String appName = 'Albania Vacation Guide';
  static const String appShortName = 'AVG';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';

  // Package Information
  static const String packageName = 'com.avg.travel';
  static const String bundleId = 'com.avg.travel';
  
  // API Configuration
  static const String strapiBaseUrl = 'https://your-strapi-instance.herokuapp.com/api';
  static const String strapiMediaUrl = 'https://your-strapi-instance.herokuapp.com';
  
  // Firebase Configuration (will be loaded from google-services.json)
  static const String firebaseProjectId = 'avg-travel-app';
  static const String firebaseStorageBucket = 'avg-travel-app.firebasestorage.app';

  // App Colors (Albania Vacation Guide Brand) - New Color Schema
  // Primary Brand Colors
  static const int primaryBlue = 0xFF0B5394; // hsl(201, 100%, 36%) light mode
  static const int primaryBlueDark = 0xFF0EA5E9; // hsl(201, 100%, 50%) dark mode
  static const int primaryForeground = 0xFFFAFAFA; // hsl(210, 40%, 98%) light mode
  static const int primaryForegroundDark = 0xFF1C1917; // hsl(222.2, 47.4%, 11.2%) dark mode

  // Legacy colors for backward compatibility
  static const int primaryTeal = 0xFF0B5394; // Using new primary blue
  static const int primaryYellow = 0xFFFADC36;
  static const int primaryOrange = 0xFFFBB73F;
  static const int primaryGreen = 0xFF00F69C;

  // Background & Surface Colors
  static const int background = 0xFFFFFFFF; // white light mode
  static const int backgroundDark = 0xFF0C0A09; // hsl(222.2, 84%, 4.9%) dark mode
  static const int foreground = 0xFF0C0A09; // hsl(222.2, 84%, 4.9%) light mode
  static const int foregroundDark = 0xFFFAFAFA; // hsl(210, 40%, 98%) dark mode

  // Secondary & Accent Colors
  static const int secondary = 0xFFF1F5F9; // hsl(210, 40%, 96.1%) light mode
  static const int secondaryDark = 0xFF292524; // hsl(217.2, 32.6%, 17.5%) dark mode
  static const int muted = 0xFFF1F5F9; // hsl(210, 40%, 96.1%) light mode
  static const int mutedDark = 0xFF292524; // hsl(217.2, 32.6%, 17.5%) dark mode

  // Text Colors
  static const int mutedForeground = 0xFF64748B; // hsl(215.4, 16.3%, 46.9%) light mode
  static const int mutedForegroundDark = 0xFF9CA3AF; // hsl(215, 20.2%, 65.1%) dark mode

  // System Colors
  static const int destructive = 0xFFEF4444; // hsl(0, 84.2%, 60.2%) light mode
  static const int destructiveDark = 0xFF7F1D1D; // hsl(0, 62.8%, 30.6%) dark mode
  static const int destructiveForeground = 0xFFFAFAFA; // hsl(210, 40%, 98%) both modes

  // Border & Input Colors
  static const int border = 0xFFE2E8F0; // hsl(214.3, 31.8%, 91.4%) light mode
  static const int borderDark = 0xFF292524; // hsl(217.2, 32.6%, 17.5%) dark mode
  static const int input = 0xFFE2E8F0; // hsl(214.3, 31.8%, 91.4%) light mode
  static const int inputDark = 0xFF292524; // hsl(217.2, 32.6%, 17.5%) dark mode
  static const int ring = 0xFF0B5394; // hsl(201, 100%, 36%) light mode
  static const int ringDark = 0xFF0EA5E9; // hsl(201, 100%, 50%) dark mode
  
  // Feature Flags
  static const bool enableOfflineMode = true;
  static const bool enablePushNotifications = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  
  // Cache Configuration
  static const int maxCacheSize = 100; // MB
  static const int cacheExpiryDays = 7;
  static const int syncIntervalMinutes = 5;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Map Configuration
  static const double defaultLatitude = 41.3275; // Albania center
  static const double defaultLongitude = 19.8187;
  static const double defaultZoom = 7.0;
  
  // Booking Configuration
  static const int maxTravelers = 20;
  static const int minAdvanceBookingDays = 1;
  static const int maxAdvanceBookingDays = 365;
  
  // File Upload Configuration
  static const int maxImageSizeMB = 10;
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Social Media Links
  static const String instagramUrl = 'https://instagram.com/avg_travel';
  static const String facebookUrl = 'https://facebook.com/avg.travel';
  static const String twitterUrl = 'https://twitter.com/avg_travel';
  static const String websiteUrl = 'https://avg.com';

  // Contact Information
  static const String supportEmail = '<EMAIL>';
  static const String businessEmail = '<EMAIL>';
  static const String phoneNumber = '+355 69 123 4567';

  // Legal
  static const String privacyPolicyUrl = 'https://avg.com/privacy';
  static const String termsOfServiceUrl = 'https://avg.com/terms';
  
  // Environment-specific configurations
  static bool get isProduction => const bool.fromEnvironment('dart.vm.product');
  static bool get isDevelopment => !isProduction;
  
  // Debug settings
  static bool get enableDebugLogs => isDevelopment;
  static bool get enableNetworkLogs => isDevelopment;
  
  // Get environment-specific Strapi URL
  static String get strapiUrl {
    if (isProduction) {
      return 'https://cms.avg.com/api';
    } else {
      return 'http://localhost:1337/api';
    }
  }

  // Get environment-specific Strapi media URL
  static String get strapiMedia {
    if (isProduction) {
      return 'https://cms.avg.com';
    } else {
      return 'http://localhost:1337';
    }
  }
}
